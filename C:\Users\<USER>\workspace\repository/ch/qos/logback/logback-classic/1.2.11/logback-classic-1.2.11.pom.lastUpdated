#NOTE: This is a Maven Resolver internal implementation file, its format can be changed without prior notice.
#Tue Jul 22 14:40:12 CST 2025
@default-alimaven-http\://maven.aliyun.com/nexus/content/groups/public/.lastUpdated=1752841243733
@default-thirdparty-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.lastUpdated=1752841243733
http\://maven.aliyun.com/nexus/content/groups/public/.error=Could not transfer artifact ch.qos.logback\:logback-classic\:pom\:1.2.11 from/to alimaven (http\://maven.aliyun.com/nexus/content/groups/public/)\: No connector factories available
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.lastUpdated=1753166412327
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.error=Could not transfer artifact ch.qos.logback\:logback-classic\:pom\:1.2.11 from/to thirdparty (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty)\: No connector factories available
