<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.github.jsqlparser</groupId>
    <artifactId>jsqlparser</artifactId>
    <version>4.5</version>
    <name>JSQLParser library</name> 
    <inceptionYear>2004</inceptionYear>
    <organization>
        <name>JSQLParser</name> 
    </organization>
    <packaging>bundle</packaging>
    <url>https://github.com/JSQLParser/JSqlParser</url>

    <licenses>
        <license>
            <name>GNU Library or Lesser General Public License (LGPL) V2.1</name>
            <url>http://www.gnu.org/licenses/lgpl-2.1.html</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <dependencies>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
        	<groupId>org.junit.jupiter</groupId>
        	<artifactId>junit-jupiter</artifactId>
        	<version>5.8.1</version>
        	<scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.16.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.10</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.200</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <developers>
        <developer>
            <name>Tobias Warneke</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <distributionManagement>
        <repository>
            <id>sonatype-nexus-staging</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
        </repository>
        <snapshotRepository>
            <id>sonatype-nexus-snapshots</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <scm>
        <connection>scm:git:https://github.com/JSQLParser/JSqlParser.git</connection>
        <developerConnection>scm:git:ssh://**************:JSQLParser/JSqlParser.git</developerConnection>
        <url>https://github.com/JSQLParser/JSqlParser.git</url>
        <tag>jsqlparser-4.5</tag>
    </scm>

    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/JSQLParser/JSqlParser/issues</url>
    </issueManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <rulesets>
                        <!-- Custom local file system rule set -->
                        <ruleset>${project.basedir}/config/pmd/ruleset.xml</ruleset>
                    </rulesets>
                    <excludes>
                        <exclude>**/*Bean.java</exclude>
                        <exclude>**/generated/*.java</exclude>
                    </excludes>
                    <excludeRoots>
                        <excludeRoot>target/generated-sources</excludeRoot>
                        <excludeRoot>target/generated-test-sources</excludeRoot>
                    </excludeRoots>
                    <printFailingErrors>true</printFailingErrors>
                </configuration>
                <executions>
                    <execution>
                        <id>pmd</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <phase>process-sources</phase>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-core</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-java</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-javascript</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                    <dependency>
                        <groupId>net.sourceforge.pmd</groupId>
                        <artifactId>pmd-jsp</artifactId>
                        <version>${pmdVersion}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/javacc/</source>
                                <source>${project.build.directory}/generated-sources/jjtree/</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <showDeprecation>true</showDeprecation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.javacc.plugin</groupId>
                <artifactId>javacc-maven-plugin</artifactId>
                <version>3.0.3</version>
                                
                <executions>
                    <execution>
                        <id>javacc</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>jjtree-javacc</goal>
                        </goals>
                    </execution>
                    
                    <!-- execute JJTree explicitely in order to generate the *.jj file needed for JJDoc -->
                    <execution>
                        <id>jjtree</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>jjtree</goal>
                        </goals>
                    </execution>    
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>net.java.dev.javacc</groupId>
                        <artifactId>javacc</artifactId>
                        <version>7.0.11</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <sourceIncludes>
                        <sourceInclude>/target/generated-sources/javacc</sourceInclude>
                    </sourceIncludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>1.17</version>
                <configuration>
                    <verbose>false</verbose>
                    <addJavaLicenseAfterPackage>false</addJavaLicenseAfterPackage>
                    <canUpdateDescription>false</canUpdateDescription>
                    <licenseName>dual_lgpl_ap2</licenseName>
                    <licenseResolver>${project.baseUri}/src/license</licenseResolver>
                </configuration>
                <executions>
                    <execution>
                        <id>first</id>
                        <goals>
                            <goal>update-file-header</goal>
                        </goals>
                        <phase>process-sources</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <localCheckout>true</localCheckout>
                    <pushChanges>false</pushChanges>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.scm</groupId>
                        <artifactId>maven-scm-provider-gitexe</artifactId>
                        <version>1.9.5</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <configuration>
                            <additionalparam>${javadoc.opts}</additionalparam>
                            <excludePackageNames>net.sf.jsqlparser.parser</excludePackageNames> 
                        </configuration>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.7.1</version>
                <executions>
                    <execution>
                        <id>attach-descriptor</id>
                        <goals>
                            <goal>attach-descriptor</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <locales>en</locales>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <format>xml</format>
                    <instrumentation>
                        <excludes>
                            <exclude>net/sf/jsqlparser/parser/*.class</exclude>
                            <exclude>net/sf/jsqlparser/JSQLParserException.class</exclude>
                        </excludes>
                    </instrumentation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>3.0.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <trimStackTrace>false</trimStackTrace>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <outputDirectory>${project.reporting.outputDirectory}/testresults</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <useStandardDocletOptions>true</useStandardDocletOptions>
                    <maxmemory>800m</maxmemory>
                    <doclint>none</doclint>
                    
                    <!-- Doclint does not work on the Test Sources
                    <doclint>all,-missing</doclint>
                    <excludePackageNames>net.sf.jsqlparser.parser</excludePackageNames>
                    <sourceFileExcludes>
                        <sourceFileExclude>src/test/**</sourceFileExclude>
                    </sourceFileExcludes>
                    -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.0.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>3.0.0</version>
            </plugin>
            
            <!-- Cobertura is broken with Java 1.8 and there is not fix
            please refer to https://github.com/cobertura/cobertura/issues/248
            
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <outputDirectory>${project.reporting.outputDirectory}/cobertura</outputDirectory>
                </configuration>
            </plugin>
            -->
            
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
            </plugin>
            
            <!-- Obsolete or Unused
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <configuration>
                    <configLocation>config/sun_checks.xml</configLocation>
                </configuration>
            </plugin>
            -->
            
            <!-- JJDoc report generating the BNF documentation -->
            <plugin>
                <groupId>org.javacc.plugin</groupId>
                <artifactId>javacc-maven-plugin</artifactId>
                <version>3.0.3</version>
                <configuration>
                    <!-- 
                    /**
                    * A flag to specify the output format for the generated documentation. If set to <code>true</code>, JJDoc will
                    * generate a plain text description of the BNF. Some formatting is done via tab characters, but the intention is to
                    * leave it as plain as possible. Specifying <code>false</code> causes JJDoc to generate a hyperlinked HTML document
                    * unless the parameter {@link #bnf} has been set to <code>true</code>. Default value is <code>false</code>.
                    * 
                    * @parameter expression="${text}"
                    */
                    -->
                    <text>false</text>
                    
                    <!--
                    /**
                    * A flag whether to generate a plain text document with the unformatted BNF. Note that setting this option to
                    * <code>true</code> is only effective if the parameter {@link #text} is <code>false</code>. Default value is
                    * <code>false</code>.
                    * 
                    * @parameter expression="${bnf}"
                    * @since 2.6
                    */
                    -->
                    <bnf>false</bnf>
                    
                    <!--
                    /**
                    * This option controls the structure of the generated HTML output. If set to <code>true</code>, a single HTML
                    * table for the entire BNF is generated. Setting it to <code>false</code> will produce one table for every
                    * production in the grammar.
                    * 
                    * @parameter expression="${oneTable}" default-value=true
                    */
                    -->
                    <oneTable>false</oneTable>
                    
                    <!--
                    /**
                    * The hypertext reference to an optional CSS file for the generated HTML documents. If specified, this CSS file
                    * will be included via a <code>&lt;link&gt;</code> element in the HTML documents. Otherwise, the default style will
                    * be used.
                    * 
                    * @parameter expression="${cssHref}"
                    * @since 2.5
                    */            
                    -->
                    <!-- <cssHref></cssHref> -->
                    
                    <outputDirectory>${project.reporting.outputDirectory}</outputDirectory>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>sign-release-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <keyname>f22e0543</keyname>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    
        <profile>
            <id>check.sources</id>
            <activation>
                <property>
                    <name>!skipCheckSources</name>
                </property>
                <jdk>[1.8,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>verify-style</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <logViolationsToConsole>true</logViolationsToConsole>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                            <sourceDirectories>${project.build.sourceDirectory}</sourceDirectories>
                            <checkstyleRules>
                                <module name="Checker">
                                    <module name="SuppressWarningsFilter" />
                                    <module name="FileTabCharacter" />
                                    <!-- git checkout may change linefeeds on the fly
                                    <module name="RegexpMultiline">
                                        <property name="format" value="(?s:(\r\n|\r).*)" />
                                        <property name="message" value="CRLF and CR line endings are prohibited, but this file uses them." />
                                    </module>
                                    -->
                                    <module name="TreeWalker">
                                        <module name="AvoidNestedBlocks" />
                                        <module name="ConstantName" />
                                        <module name="EmptyCatchBlock" />
                                        <module name="EmptyStatement" />
                                        <module name="MissingOverride" />
                                        <module name="MultipleVariableDeclarations" />
                                        <module name="ParameterAssignment" />
                                        <module name="StringLiteralEquality" />
                                        <module name="RedundantImport" />
                                        <module name="UnusedImports" />
                                        
                                        <module name="WhitespaceAfter" />
										
                                        <module name="NeedBraces" />
                                        <module name="UnnecessaryParentheses" />
                                        <module name="LeftCurly" />
                                        <module name="RightCurly" />
                                        
                                        <module name="SuppressWarningsHolder" />
                                    </module>
                                </module>
                            </checkstyleRules>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>com.puppycrawl.tools</groupId>
                                <artifactId>checkstyle</artifactId>
                                <version>8.29</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- for building the parser with debug flags -->
            <id>skip.all</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <checkstyle.skip>true</checkstyle.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <license.skipUpdateLicense>true</license.skipUpdateLicense>
            </properties>
        </profile>
    </profiles>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <pmdVersion>6.46.0</pmdVersion>
    </properties>

    <description>JSqlParser parses an SQL statement and translate it into a hierarchy of Java classes.
        The generated hierarchy can be navigated using the Visitor Pattern.</description>
</project>
