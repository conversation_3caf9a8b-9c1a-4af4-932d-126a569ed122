#NOTE: This is a Maven Resolver internal implementation file, its format can be changed without prior notice.
#Fri Jul 18 20:13:58 CST 2025
@default-nexus-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.lastUpdated=1752840818002
@default-thirdparty-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.lastUpdated=1752840838011
http\://maven.aliyun.com/nexus/content/groups/public/.lastUpdated=1752840838073
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.error=Could not transfer artifact com.fasterxml.jackson.datatype\:jackson-datatype-jdk8\:pom\:2.13.3 from/to nexus (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/)\: No route to host
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.error=Could not transfer artifact com.fasterxml.jackson.datatype\:jackson-datatype-jdk8\:pom\:2.13.3 from/to thirdparty (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty)\: No route to host
