<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-dataformats-text</artifactId>
    <version>2.13.3</version>
  </parent>
  <artifactId>jackson-dataformat-yaml</artifactId>
  <packaging>bundle</packaging>
  <name>Jackson-dataformat-YAML</name>
  <description>Support for reading and writing YAML-encoded data via Jackson abstractions.
  </description>
  <url>https://github.com/FasterXML/jackson-dataformats-text</url>

  <properties>
    <packageVersion.dir>com/fasterxml/jackson/dataformat/yaml</packageVersion.dir>
    <packageVersion.package>${project.groupId}.yaml</packageVersion.package>
  </properties>

  <dependencies>
    <!-- Extends Jackson core, databind (optional); uses SnakeYAML for parsing, generation -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <!-- starting with 2.10(.4 patch), not optional but fully required -->
    </dependency>

    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.30</version>
    </dependency>

     <!-- and for testing need annotations; but should be available via `jackson-databind` above
       -->
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <!--  19-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
