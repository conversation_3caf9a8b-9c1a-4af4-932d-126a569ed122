<?xml version="1.0" encoding="utf-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
      <groupId>org.sonatype.oss</groupId>
      <artifactId>oss-parent</artifactId>
      <version>9</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.aliyun.openservices</groupId>
    <artifactId>ons-parent</artifactId>
    <version>1.8.0.Final</version>
    <packaging>pom</packaging>
    <name>ons-parent ${project.version}</name>
    <description>Open Notification Service, Apache RocketMQ Commercial Distribution. This distribution include all of
        the features of the open source version, with some additional, higher management features.
    </description>
    <inceptionYear>2017</inceptionYear>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <file_encoding>UTF-8</file_encoding>
        <!-- Compiler settings properties -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <eagleeye.core.version>1.4.8</eagleeye.core.version>
    </properties>
    <modules>
        <module>ons-core</module>
        <!--<module>ons-top</module>-->
        <!--<module>ons-housekeeping</module>-->
        <!--<module>ons-auth</module>-->
        <!--<module>ons-instance</module>-->
    </modules>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.3</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <version>2.7</version>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <compilerVersion>${maven.compiler.source}</compilerVersion>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                    <encoding>${file_encoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <argLine>-Xms512m -Xmx1024m</argLine>
                    <forkMode>always</forkMode>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.6</version>
                <configuration>
                    <locales>en_US</locales>
                    <outputEncoding>UTF-8</outputEncoding>
                    <inputEncoding>UTF-8</inputEncoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <configuration>
                    <charset>UTF-8</charset>
                    <locale>en_US</locale>
                </configuration>
                <executions>
                    <execution>
                        <id>aggregate</id>
                        <goals>
                            <goal>aggregate</goal>
                        </goals>
                        <phase>site</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.4</version>
            </plugin>
            <plugin>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.17</version>
                <executions>
                    <execution>
                        <id>verify</id>
                        <phase>verify</phase>
                        <configuration>
                            <configLocation>style/ons_checkstyle.xml</configLocation>
                            <encoding>UTF-8</encoding>
                            <consoleOutput>true</consoleOutput>
                            <failsOnError>true</failsOnError>
                            <includeTestSourceDirectory>false</includeTestSourceDirectory>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <prefix>git</prefix>
                    <dateFormat>yyyy-MM-dd HH:mm:ss z</dateFormat>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                    <skipPoms>false</skipPoms>
                    <verbose>false</verbose>
                    <generateGitPropertiesFile>false</generateGitPropertiesFile>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            <addClasspath>false</addClasspath>
                        </manifest>
                        <manifestEntries>
                            <Project-Version>${project.version}</Project-Version>
                            <Git-Revision>${git.commit.id}</Git-Revision>
                            <Build-Branch>${git.branch}</Build-Branch>
                            <Build-Timestamp>${build.timestamp}</Build-Timestamp>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.7</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.1.11</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.1.11</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.9</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>18.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.50</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.11</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>2.6.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.6.3</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>butler-noc-sdk</artifactId>
                <version>2.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.eagleeye</groupId>
                <artifactId>eagleeye-core</artifactId>
                <version>${eagleeye.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.security</groupId>
                <artifactId>security-spring-dependencies</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>3.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.sharing.platform</groupId>
                <artifactId>dauth-wrapper</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.6.2</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ons-inner</artifactId>
                <version>1.0.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <scm>
        <url>**************************:middleware/ons.git</url>
        <connection>scm:git:**************************:middleware/ons.git</connection>
        <developerConnection>scm:git:**************************:middleware/ons.git</developerConnection>
    </scm>
</project>
