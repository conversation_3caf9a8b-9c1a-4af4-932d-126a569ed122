<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.aliyun.openservices</groupId>
        <artifactId>ons-parent</artifactId>
        <version>1.8.0.Final</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <inceptionYear>2012</inceptionYear>
    <artifactId>ons-all</artifactId>
    <packaging>pom</packaging>
    <name>ons-all ${project.version}</name>
    <url>https://github.com/alibaba/rocketmq</url>
    <description>https://github.com/alibaba/RocketMQ/blob/develop/README.md</description>
    <modules>
        <module>ons-api</module>
        <module>ons-client</module>
        <module>ons-client-ext</module>
        <!--<module>ons-jms</module>-->
        <!--<module>ons-broker</module>-->
        <!--<module>ons-namesrv</module>-->
        <!--<module>ons-retry</module>-->
        <!--<module>ons-sdk</module>-->
        <!--<module>ons-tools</module>-->
        <!--<module>ons-sdk-plugin</module>-->
        <!--<module>ons-transaction</module>-->
        <!--<module>ons-timer</module>-->
        <!--<module>ons-common</module>-->
        <!--<module>ons-log-appender</module>-->
        <!--<module>ons-spring-jms</module>-->
        <module>ons-trace-core</module>
        <module>ons-auth4client</module>
    </modules>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--maven properties -->
        <maven.test.skip>true</maven.test.skip>
        <maven.jdoc.skip>false</maven.jdoc.skip>
        <downloadSources>true</downloadSources>
        <!-- compiler settings properties -->
        <java_source_version>1.6</java_source_version>
        <java_target_version>1.6</java_target_version>
        <file_encoding>UTF-8</file_encoding>
        <!-- Always use stable version of RocketMQ -->
        <rocketmq.version>4.3.4</rocketmq.version>
        <auth.version>${project.version}</auth.version>
        <spring.version>4.1.2.RELEASE</spring.version>
        <diamond.version>3.7.4</diamond.version>
    </properties>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>${java_source_version}</source>
                    <target>${java_target_version}</target>
                    <encoding>${file_encoding}</encoding>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <skip>${maven.test.skip}</skip>
                    <argLine>-Xms512m -Xmx1024m</argLine>
                    <forkMode>once</forkMode>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <excludes>
                        <exclude>com/alibaba/rocketmq/remoting/ExceptionTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/remoting/SyncInvokeTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/remoting/NettyIdleTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/remoting/NettyConnectionTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/common/filter/PolishExprTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/common/protocol/MQProtosHelperTest.java</exclude>
                        <exclude>
                            com/alibaba/rocketmq/client/consumer/loadbalance/AllocateMessageQueueAveragelyTest.java
                        </exclude>
                        <exclude>com/alibaba/rocketmq/store/RecoverTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/broker/api/SendMessageTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/integration/*/*.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/integration/BaseTest.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/*/*.java</exclude>
                        <exclude>com/alibaba/rocketmq/test/BaseTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skip>${maven.jdoc.skip}</skip>
                    <encoding>${file_encoding}</encoding>
                    <charset>${file_encoding}</charset>
                    <doclet>org.jboss.apiviz.APIviz</doclet>
                    <docletArtifact>
                        <groupId>org.jboss.apiviz</groupId>
                        <artifactId>apiviz</artifactId>
                        <version>1.3.2.GA</version>
                    </docletArtifact>
                    <useStandardDocletOptions>true</useStandardDocletOptions>
                    <breakiterator>true</breakiterator>
                    <version>true</version>
                    <author>true</author>
                    <keywords>true</keywords>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>rmq</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>rmq</finalName>
                            <descriptors>
                                <descriptor>release.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>ons-namesrv</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>ons-namesrv</finalName>
                            <descriptors>
                                <descriptor>release-namesrv.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>ons-broker</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>ons-broker</finalName>
                            <descriptors>
                                <descriptor>release-broker.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>ons-client</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>aliyun-ons-client-java</finalName>
                            <descriptors>
                                <descriptor>release-client.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>ons-exactlyonce</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>aliyun-ons-exactlyonce-client-java</finalName>
                            <descriptors>
                                <descriptor>release-client.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.7</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-exactlyonce</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-jms</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-retry</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-example</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-broker</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-namesrv</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-auth</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-trace-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.rocketmq</groupId>
                <artifactId>rocketmq-broker</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.rocketmq</groupId>
                <artifactId>rocketmq-namesrv</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.rocketmq</groupId>
                <artifactId>rocketmq-remoting</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.rocketmq</groupId>
                <artifactId>rocketmq-common</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.rocketmq</groupId>
                <artifactId>rocketmq-tools</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>ons-auth4client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.notify</groupId>
                <artifactId>notify-tr-client</artifactId>
                <version>1.8.19.26</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.diamond</groupId>
                <artifactId>diamond-client</artifactId>
                <version>${diamond.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.alimonitor</groupId>
                <artifactId>alimonitor-jmonitor</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.31</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.top</groupId>
                <artifactId>top-api-sdk-dev</artifactId>
                <version>top-api-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>tls-decrypt</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.googlecode.guava-osgi</groupId>
                        <artifactId>guava-osgi</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
                <artifactId>concurrentlinkedhashmap-lru</artifactId>
                <version>1.4.2</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.1.11</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.1.11</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
