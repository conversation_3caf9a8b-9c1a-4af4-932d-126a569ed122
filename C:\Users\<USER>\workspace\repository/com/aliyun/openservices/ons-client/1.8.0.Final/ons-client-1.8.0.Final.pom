<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>ons-all</artifactId>
    <groupId>com.aliyun.openservices</groupId>
    <version>1.8.0.Final</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>ons-client</artifactId>
  <name>ons-client ${project.version}</name>
  <profiles>
    <profile>
      <id>client-shade</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-shade-plugin</artifactId>
            <version>3.0.0</version>
            <executions>
              <execution>
                <goals>
                  <goal>shade</goal>
                </goals>
                <configuration>
                  <keepDependenciesWithProvidedScope>false</keepDependenciesWithProvidedScope>
                  <promoteTransitiveDependencies>false</promoteTransitiveDependencies>
                  <createDependencyReducedPom>true</createDependencyReducedPom>
                  <minimizeJar>false</minimizeJar>
                  <createSourcesJar>true</createSourcesJar>
                  <shadeSourcesContent>true</shadeSourcesContent>
                  <artifactSet>
                    <includes>
                      <include>com.alibaba:fastjson</include>
                      <include>io.netty:netty-all</include>
                      <include>com.alibaba.rocketmq:rocketmq-logging</include>
                      <include>com.alibaba.rocketmq:rocketmq-common</include>
                      <include>com.alibaba.rocketmq:rocketmq-remoting</include>
                      <include>com.alibaba.rocketmq:rocketmq-client</include>
                      <include>commons-codec:commons-codec</include>
                      <include>org.apache.commons:commons-lang3</include>
                      <include>com.aliyun.openservices:ons-api</include>
                      <include>com.aliyun.openservices:ons-auth4client</include>
                      <include>com.aliyun.openservices:ons-trace-core</include>
                    </includes>
                  </artifactSet>
                  <relocations>
                    <relocation>
                      <pattern>io.netty</pattern>
                      <shadedPattern>com.aliyun.openservices.shade.io.netty</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>com.alibaba.fastjson</pattern>
                      <shadedPattern>com.aliyun.openservices.shade.com.alibaba.fastjson</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>com.alibaba.rocketmq</pattern>
                      <shadedPattern>com.aliyun.openservices.shade.com.alibaba.rocketmq</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.apache.commons</pattern>
                      <shadedPattern>com.aliyun.openservices.shade.org.apache.commons</shadedPattern>
                    </relocation>
                  </relocations>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <keepDependenciesWithProvidedScope>false</keepDependenciesWithProvidedScope>
              <promoteTransitiveDependencies>false</promoteTransitiveDependencies>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <minimizeJar>false</minimizeJar>
              <createSourcesJar>true</createSourcesJar>
              <shadeSourcesContent>true</shadeSourcesContent>
              <artifactSet>
                <includes>
                  <include>com.alibaba:fastjson</include>
                  <include>io.netty:netty-all</include>
                  <include>com.alibaba.rocketmq:rocketmq-logging</include>
                  <include>com.alibaba.rocketmq:rocketmq-common</include>
                  <include>com.alibaba.rocketmq:rocketmq-remoting</include>
                  <include>com.alibaba.rocketmq:rocketmq-client</include>
                  <include>commons-codec:commons-codec</include>
                  <include>org.apache.commons:commons-lang3</include>
                  <include>com.aliyun.openservices:ons-api</include>
                  <include>com.aliyun.openservices:ons-auth4client</include>
                  <include>com.aliyun.openservices:ons-trace-core</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>io.netty</pattern>
                  <shadedPattern>com.aliyun.openservices.shade.io.netty</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.alibaba.fastjson</pattern>
                  <shadedPattern>com.aliyun.openservices.shade.com.alibaba.fastjson</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.alibaba.rocketmq</pattern>
                  <shadedPattern>com.aliyun.openservices.shade.com.alibaba.rocketmq</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.apache.commons</pattern>
                  <shadedPattern>com.aliyun.openservices.shade.org.apache.commons</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.6.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>

