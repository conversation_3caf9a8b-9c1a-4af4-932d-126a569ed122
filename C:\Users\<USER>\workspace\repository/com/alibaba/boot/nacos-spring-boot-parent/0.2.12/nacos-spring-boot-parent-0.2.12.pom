<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at
      http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.alibaba.boot</groupId>
    <artifactId>nacos-spring-boot-project</artifactId>
    <version>0.2.12</version>
  </parent>
  <groupId>com.alibaba.boot</groupId>
  <artifactId>nacos-spring-boot-parent</artifactId>
  <version>0.2.12</version>
  <packaging>pom</packaging>
  <name>Nacos Spring Boot Parent</name>
  <description>Nacos Spring Boot Parent</description>
  <properties>
    <nacos-spring-context.version>1.1.1</nacos-spring-context.version>
    <spring-boot.version>2.6.3</spring-boot.version>
    <maven-compiler-plugin.version>3.8.0</maven-compiler-plugin.version>
    <maven-jacoco-plugin.version>0.8.1</maven-jacoco-plugin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.target.version>1.8</java.target.version>
    <maven-gpg-plugin.version>1.5</maven-gpg-plugin.version>
    <apache-rat-plugin.version>0.12</apache-rat-plugin.version>
    <java.version>1.8</java.version>
    <maven-surefire-plugin.version>2.19.1</maven-surefire-plugin.version>
    <nacos.version>2.1.0</nacos.version>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
    <argline>-server -Xms256m -Xmx512m -XX:PermSize=64m -XX:MaxPermSize=128m -Dfile.encoding=UTF-8
            -Djava.net.preferIPv4Stack=true</argline>
    <maven-jar-plugin.version>3.0.2</maven-jar-plugin.version>
    <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
    <java.source.version>1.8</java.source.version>
    <arguments></arguments>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-spring-context</artifactId>
        <version>${nacos-spring-context.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>nacos-client</artifactId>
            <groupId>com.alibaba.nacos</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-client</artifactId>
        <version>${nacos.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.boot</groupId>
        <artifactId>nacos-spring-boot-base</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.boot</groupId>
        <artifactId>nacos-config-spring-boot-autoconfigure</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.boot</groupId>
        <artifactId>nacos-discovery-spring-boot-autoconfigure</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <url>https://repo.maven.apache.org/maven2</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestone</id>
      <name>Spring Milestone</name>
      <url>http://repo.spring.io/milestone</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>spring-snapshot</id>
      <name>Spring Snapshot</name>
      <url>http://repo.spring.io/snapshot</url>
    </repository>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>rabbit-milestone</id>
      <name>Rabbit Milestone</name>
      <url>https://dl.bintray.com/rabbitmq/maven-milestones</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
    <pluginRepository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestone</id>
      <name>Spring Milestone</name>
      <url>http://repo.spring.io/milestone</url>
    </pluginRepository>
    <pluginRepository>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>spring-snapshot</id>
      <name>Spring Snapshot</name>
      <url>http://repo.spring.io/snapshot</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>src/main/resources/</directory>
      </resource>
      <resource>
        <targetPath>META-INF/</targetPath>
        <filtering>false</filtering>
        <directory>../</directory>
        <includes>
          <include>NOTICE</include>
          <include>LICENSE</include>
        </includes>
      </resource>
      <resource>
        <targetPath>META-INF/</targetPath>
        <filtering>false</filtering>
        <directory>../../</directory>
        <includes>
          <include>NOTICE</include>
          <include>LICENSE</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
        <configuration>
          <archive>
            <addMavenDescriptor>true</addMavenDescriptor>
            <index>true</index>
            <manifest>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <manifestEntries>
              <Specification-Version>${project.version}</Specification-Version>
              <Implementation-Version>${project.version}</Implementation-Version>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <fork>true</fork>
          <source>${java.source.version}</source>
          <target>${java.target.version}</target>
          <encoding>${file.encoding}</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${maven-jacoco-plugin.version}</version>
        <executions>
          <execution>
            <id>jacoco-initialize</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
            <configuration>
              <propertyName>jacocoArgLine</propertyName>
            </configuration>
          </execution>
          <execution>
            <id>jacoco-site</id>
            <phase>package</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-rules</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>[1.8,)</version>
                </requireJavaVersion>
                <requireProperty>
                  <property>project.name</property>
                </requireProperty>
                <requireProperty>
                  <property>project.description</property>
                </requireProperty>
              </rules>
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven-source-plugin.version}</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
        <configuration>
          <useSystemClassLoader>true</useSystemClassLoader>
          <forkMode>once</forkMode>
          <argLine>${argline} ${jacocoArgLine}</argLine>
          <systemProperties>
            <property>
              <name>transporter</name>
              <value>${transporter}</value>
            </property>
            <property>
              <name>serialization</name>
              <value>${serialization}</value>
            </property>
            <property>
              <name>port</name>
              <value>${port}</value>
            </property>
            <property>
              <name>threadpool</name>
              <value>${threadpool}</value>
            </property>
            <property>
              <name>threads</name>
              <value>${threads}</value>
            </property>
            <property>
              <name>iothreads</name>
              <value>${iothreads}</value>
            </property>
            <property>
              <name>server</name>
              <value>${server}</value>
            </property>
            <property>
              <name>timeout</name>
              <value>${timeout}</value>
            </property>
            <property>
              <name>length</name>
              <value>${length}</value>
            </property>
            <property>
              <name>connections</name>
              <value>${connections}</value>
            </property>
            <property>
              <name>base</name>
              <value>${base}</value>
            </property>
            <property>
              <name>concurrent</name>
              <value>${concurrent}</value>
            </property>
            <property>
              <name>runs</name>
              <value>${runs}</value>
            </property>
            <property>
              <name>onerror</name>
              <value>${onerror}</value>
            </property>
          </systemProperties>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${apache-rat-plugin.version}</version>
        <executions>
          <execution>
            <id>verify.rat</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <excludes>
                <exclude>**/*.versionsBackup</exclude>
                <exclude>**/.idea/</exclude>
                <exclude>**/*.iml</exclude>
                <exclude>**/*.txt</exclude>
                <exclude>**/*.load</exclude>
                <exclude>**/*.flex</exclude>
                <exclude>**/*.fc</exclude>
                <exclude>**/*.javascript</exclude>
                <exclude>**/*.properties</exclude>
                <exclude>**/*.thrift</exclude>
                <exclude>**/*.sh</exclude>
                <exclude>**/*.bat</exclude>
                <exclude>**/*.md</exclude>
                <exclude>.git/</exclude>
                <exclude>.gitignore</exclude>
                <exclude>.repository/</exclude>
                <exclude>**/.settings/*</exclude>
                <exclude>**/.classpath</exclude>
                <exclude>**/.project</exclude>
                <exclude>**/target/**</exclude>
                <exclude>**/*.log</exclude>
                <exclude>CODE_OF_CONDUCT.md</exclude>
                <exclude>.codecov.yml</exclude>
                <exclude>.travis.yml</exclude>
                <exclude>PULL_REQUEST_TEMPLATE.md</exclude>
                <exclude>CONTRIBUTING.md</exclude>
                <exclude>README.md</exclude>
                <exclude>README_CN.md</exclude>
                <exclude>**/codestyle/*</exclude>
                <exclude>**/resources/META-INF/**</exclude>
                <exclude>**/*.factories</exclude>
                <exclude>**/*.provides</exclude>
                <exclude>**/*.properties</exclude>
                <exclude>**/*.json</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <version>${maven-release-plugin.version}</version>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <useReleaseProfile>false</useReleaseProfile>
          <releaseProfiles>release</releaseProfiles>
          <goals>deploy</goals>
          <arguments>${arguments}</arguments>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
