<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>
  <groupId>com.alibaba.boot</groupId>
  <artifactId>nacos-spring-boot-project</artifactId>
  <version>0.2.12</version>
  <packaging>pom</packaging>
  <name>Nacos Spring Boot Project</name>
  <description>Nacos Spring Boot Project</description>
  <url>https://github.com/nacos-group/nacos-spring-project</url>
  <organization />
  <developers>
    <developer>
      <id>Jim</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <url>https://github.com/fangjian0423</url>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <modules>
    <module>nacos-spring-boot-parent</module>
    <module>nacos-spring-boot-base</module>
    <module>nacos-config-spring-boot-autoconfigure</module>
    <module>nacos-config-spring-boot-starter</module>
    <module>nacos-config-spring-boot-actuator</module>
    <module>nacos-discovery-spring-boot-autoconfigure</module>
    <module>nacos-discovery-spring-boot-starter</module>
    <module>nacos-discovery-spring-boot-actuator</module>
    <module>nacos-spring-boot-samples</module>
  </modules>
  <scm>
    <connection>scm:git:git:////github.com/nacos-group/nacos-spring-boot-project.git</connection>
    <developerConnection>scm:git:ssh://git@//github.com/nacos-group/nacos-spring-boot-project.git</developerConnection>
    <url>https://github.com/nacos-group/nacos-spring-boot-project</url>
  </scm>
  <issueManagement>
    <system>Github</system>
    <url>https://github.com/nacos-group/nacos-spring-boot-project/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>sona</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sona</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <maven-source-plugin.version>3.1.0</maven-source-plugin.version>
    <revision>0.2.12</revision>
    <maven-flatten-version>1.1.0</maven-flatten-version>
    <maven_javadoc_version>3.0.1</maven_javadoc_version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven_surefire_version>2.19.1</maven_surefire_version>
  </properties>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven-source-plugin.version}</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${maven-flatten-version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
          <pomElements>
            <dependencies>expand</dependencies>
          </pomElements>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.4.0</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <doclint>none</doclint>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <doclint>none</doclint>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
