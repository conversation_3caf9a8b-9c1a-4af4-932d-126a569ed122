<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.alibaba</groupId>
	<artifactId>druid</artifactId>
	<version>1.0.26</version>

	<packaging>jar</packaging>
	<name>druid</name>
	<description>An JDBC datasource implementation.</description>
	<url>https://github.com/alibaba/druid</url>
	<inceptionYear>2013</inceptionYear>

	<properties>
		<spring.version>4.2.5.RELEASE</spring.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<developers>
		<developer>
			<id>wenshao</id>
			<name>we<PERSON>hao</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<id>kiki</id>
			<name>kiki</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<id>yako</id>
			<name>yako</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<scm>
		<url>https://<EMAIL>/alibaba/druid.git</url>
		<connection>scm:git:https://<EMAIL>/alibaba/druid.git</connection>
	</scm>

	<organization>
		<name>Alibaba Group</name>
		<url>http://code.alibabatech.com/</url>
	</organization>

	<licenses>
		<license>
			<name>Apache 2</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
			<comments>A business-friendly OSS license</comments>
		</license>
	</licenses>

	<issueManagement>
		<system>Jira</system>
		<url>http://code.alibabatech.com/jira/browse/DRUID</url>
	</issueManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.0</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.2.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<attach>true</attach>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.10.3</version>
				<executions>
					<execution>
						<id>attach-javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<show>public</show>
					<charset>UTF-8</charset>
					<encoding>UTF-8</encoding>
					<docencoding>UTF-8</docencoding>
					<links>
						<link>http://docs.oracle.com/javase/6/docs/api</link>
					</links>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.4</version>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!-- http://code.google.com/p/maven-license-plugin/wiki/HowTo -->
			<plugin>
				<!-- Working with multi-module projects -->
				<inherited>false</inherited>
				<groupId>com.mycila.maven-license-plugin</groupId>
				<artifactId>maven-license-plugin</artifactId>
				<version>1.10.b1</version>
				<configuration>
					<header>${basedir}/doc/license.txt</header>
					<failIfMissing>true</failIfMissing>
					<aggregate>true</aggregate>
					<strictCheck>true</strictCheck>
					<properties>
						<!-- <year>2012</year> -->
					</properties>
					<mapping>
						<java>SLASHSTAR_STYLE</java>
					</mapping>
					<includes>
						<include>src/main/java/**/*.java</include>
					</includes>
					<encoding>UTF-8</encoding>
				</configuration>
				<executions>
					<execution>
						<id>check-headers</id>
						<phase>verify</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<Implementation-Build>${buildNumber}</Implementation-Build>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.1</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<format>{0,date,yyyy-MM-dd HH:mm:ss}</format>
					<items>
						<item>timestamp</item>
					</items>
					<doCheck>false</doCheck>
					<doUpdate>true</doUpdate>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>default-profile</id>
			<activation>
				<activeByDefault>true</activeByDefault>
				<file>
					<exists>${env.JAVA_HOME}/lib/jconsole.jar</exists>
				</file>
			</activation>
			<properties>
				<toolsjar>${env.JAVA_HOME}/lib/tools.jar</toolsjar>
				<jconsolejar>${env.JAVA_HOME}/lib/jconsole.jar</jconsolejar>
			</properties>

			<dependencies>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>jconsole</artifactId>
					<version>1.6.0</version>
					<scope>system</scope>
					<systemPath>${jconsolejar}</systemPath>
					<optional>true</optional>
				</dependency>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>tools</artifactId>
					<version>1.6.0</version>
					<scope>system</scope>
					<systemPath>${toolsjar}</systemPath>
					<optional>true</optional>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>mac-profile</id>
			<activation>
				<activeByDefault>false</activeByDefault>
				<file>
					<exists>${java.home}/../Classes/jconsole.jar</exists>
				</file>
			</activation>
			<dependencies>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>jconsole</artifactId>
					<version>1.6.0</version>
					<scope>system</scope>
					<systemPath>${java.home}/../Classes/jconsole.jar</systemPath>
				</dependency>
			</dependencies>
		</profile>

		<profile>
			<id>mac-profile-oracle-jdk</id>
			<activation>
				<activeByDefault>false</activeByDefault>
				<file>
					<exists>${java.home}/../lib/jconsole.jar</exists>
				</file>
			</activation>
			<dependencies>
				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>jconsole</artifactId>
					<version>1.8.0</version>
					<scope>system</scope>
					<systemPath>${java.home}/../lib/jconsole.jar</systemPath>
				</dependency>

				<dependency>
					<groupId>com.alibaba</groupId>
					<artifactId>tools</artifactId>
					<version>1.8.0</version>
					<scope>system</scope>
					<systemPath>${java.home}/../lib/tools.jar</systemPath>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>travis</id>
			<activation>
				<property>
					<name>env.TRAVIS</name>
					<value>true</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<!-- for codecov.io -->
						<!-- config example: https://github.com/codecov/example-java -->
						<!-- plugin docs: http://eclemma.org/jacoco/trunk/doc/ -->
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.7.6.201602180812</version>
						<executions>
							<execution>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
							</execution>
							<execution>
								<id>report</id>
								<phase>test</phase>
								<goals>
									<goal>report</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>simple-jndi</groupId>
			<artifactId>simple-jndi</artifactId>
			<version>0.11.4.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>jta</artifactId>
			<version>1.1</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.2</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>${spring.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-ibatis</artifactId>
			<version>2.0.8</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>3.4.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
			<version>1.3.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.9</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.9</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.5</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.5</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.39</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>net.sourceforge.jtds</groupId>
			<artifactId>jtds</artifactId>
			<version>1.3.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>9.1-901-1.jdbc4</version>
			<scope>provided</scope>
		</dependency>
		
		<dependency>
			<groupId>com.oracle</groupId>
			<artifactId>ojdbc6</artifactId>
			<version>11.2.0.3</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.2.2</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.ibatis</groupId>
			<artifactId>ibatis-sqlmap</artifactId>
			<version>2.3.4.726</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<version>1.4.191</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>5.1.0.Final</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-c3p0</artifactId>
			<version>5.1.0.Final</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.aliyun.odps</groupId>
			<artifactId>odps-sdk-udf</artifactId>
			<version>0.17.3</version>
			<scope>provided</scope>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>javax.validation</groupId>
					<artifactId>validation-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
</project>
