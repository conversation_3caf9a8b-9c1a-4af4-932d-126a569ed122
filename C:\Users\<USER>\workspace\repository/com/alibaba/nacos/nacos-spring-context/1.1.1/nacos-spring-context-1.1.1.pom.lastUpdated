#NOTE: This is a Maven Resolver internal implementation file, its format can be changed without prior notice.
#Tue Jul 22 14:40:11 CST 2025
@default-alimaven-http\://maven.aliyun.com/nexus/content/groups/public/.lastUpdated=1752841243729
@default-rabbit-milestone-https\://dl.bintray.com/rabbitmq/maven-milestones/.lastUpdated=1752841243730
@default-spring-milestone-http\://repo.spring.io/milestone/.lastUpdated=1752841243730
@default-spring-snapshot-http\://repo.spring.io/snapshot/.lastUpdated=1752841243730
@default-thirdparty-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.lastUpdated=1752841243729
http\://maven.aliyun.com/nexus/content/groups/public/.error=Could not transfer artifact com.alibaba.nacos\:nacos-spring-context\:pom\:1.1.1 from/to alimaven (http\://maven.aliyun.com/nexus/content/groups/public/)\: No connector factories available
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.lastUpdated=1753166411564
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.error=Could not transfer artifact com.alibaba.nacos\:nacos-spring-context\:pom\:1.1.1 from/to thirdparty (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty)\: No connector factories available
http\://repo.spring.io/milestone/.error=Could not transfer artifact com.alibaba.nacos\:nacos-spring-context\:pom\:1.1.1 from/to spring-milestone (http\://repo.spring.io/milestone)\: No connector factories available
http\://repo.spring.io/snapshot/.error=Could not transfer artifact com.alibaba.nacos\:nacos-spring-context\:pom\:1.1.1 from/to spring-snapshot (http\://repo.spring.io/snapshot)\: No connector factories available
https\://dl.bintray.com/rabbitmq/maven-milestones/.error=Could not transfer artifact com.alibaba.nacos\:nacos-spring-context\:pom\:1.1.1 from/to rabbit-milestone (https\://dl.bintray.com/rabbitmq/maven-milestones)\: No connector factories available
