<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>

    <groupId>com.alibaba.nacos</groupId>
    <artifactId>nacos-spring-parent</artifactId>
    <version>1.1.1</version>
    <name>Alibaba Nacos :: Spring :: POM</name>
    <packaging>pom</packaging>


    <modules>
        <module>nacos-spring-context</module>
        <module>nacos-spring-samples</module>
    </modules>

    <scm>
        <url>**************:nacos-group/nacos-spring-project.git</url>
        <connection>scm:**************:nacos-group/nacos-spring-project.git</connection>
        <developerConnection>scm:**************:nacos-group/nacos-spring-project.git
        </developerConnection>
    </scm>

    <mailingLists>
        <mailingList>
            <name>Development List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
        <mailingList>
            <name>User List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
        <mailingList>
            <name>Commits List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>

    <developers>

        <developer>
            <id>Alibaba Nacos</id>
            <name>Nacos</name>
            <url>http://nacos.io</url>
            <email><EMAIL></email>
        </developer>

        <developer>
            <id>mercyblitz</id>
            <name>小马哥</name>
            <url>http://nacos.io</url>
            <email><EMAIL></email>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
        <argLine>-Dfile.encoding=UTF-8</argLine>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- Compiler settings properties -->
        <maven.compiler.source>1.6</maven.compiler.source>
        <maven.compiler.target>1.6</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <!-- Exclude all generated code -->
        <sonar.jacoco.itReportPath>${project.basedir}/../test/target/jacoco-it.exec
        </sonar.jacoco.itReportPath>
        <sonar.exclusions>file:**/generated-sources/**,**/test/**</sonar.exclusions>
        <!-- Nacos -->
        <nacos.version>1.4.1</nacos.version>
        <!-- Spring -->
        <spring.framework.version>3.2.18.RELEASE</spring.framework.version>
        <!--snake-yaml-->
        <snake.yaml.version>1.19</snake.yaml.version>
        <!-- Servlet -->
        <servlet-api.version>3.0.1</servlet-api.version>
        <!-- javax-->
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
        <!-- apache commons-lang3 -->
        <commons-lang3.version>3.4</commons-lang3.version>
        <!-- Alibaba's Spring Context Support -->
        <spring-context-support.version>1.0.11</spring-context-support.version>
    </properties>

    <!-- 管理依赖版本号,子项目不会默认依赖 -->
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet-api.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snake.yaml.version}</version>
            </dependency>

            <!-- Alibaba's Spring Context Support -->
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-context-support.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax.annotation-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- Spring Framework -->

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.framework.version}</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.framework.version}</version>
                <optional>true</optional>
            </dependency>

            <!-- Testing -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.framework.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.15.0</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>

            <distributionManagement>
                <snapshotRepository>
                    <id>sona</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
                <repository>
                    <id>sona</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2
                    </url>
                </repository>
            </distributionManagement>

        </profile>

        <profile>
            <id>spring-4.0</id>
            <properties>
                <spring.framework.version>4.0.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-4.1</id>
            <properties>
                <spring.framework.version>4.1.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-4.2</id>
            <properties>
                <spring.framework.version>4.2.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-4.3</id>
            <properties>
                <spring.framework.version>4.3.29.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-5.0</id>
            <properties>
                <spring.framework.version>5.0.19.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-5.1</id>
            <properties>
                <spring.framework.version>5.1.18.RELEASE</spring.framework.version>
            </properties>
        </profile>

        <profile>
            <id>spring-5.2</id>
            <properties>
                <spring.framework.version>5.2.9.RELEASE</spring.framework.version>
            </properties>
        </profile>

    </profiles>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
        </plugins>
    </reporting>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <configuration>
                    <useProjectReferences>false</useProjectReferences>
                    <additionalConfig>
                        <file>
                            <name>.settings/org.eclipse.jdt.ui.prefs</name>
                            <location>
                                ${project.basedir}/eclipse/org.eclipse.jdt.ui.prefs
                            </location>
                        </file>
                        <file>
                            <name>.settings/org.eclipse.jdt.core.prefs</name>
                            <location>
                                ${project.basedir}/eclipse/org.eclipse.jdt.core.prefs
                            </location>
                        </file>
                    </additionalConfig>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
