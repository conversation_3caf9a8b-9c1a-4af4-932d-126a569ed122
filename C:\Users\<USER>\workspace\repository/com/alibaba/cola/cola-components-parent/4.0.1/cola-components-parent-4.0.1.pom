<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.alibaba.cola</groupId>
    <artifactId>cola-components-parent</artifactId>
    <version>4.0.1</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}:${project.version}</name>
    <description>${project.artifactId}</description>
    <url>https://github.com/alibaba/COLA</url>

    <licenses>
        <license>
            <name>GNU Lesser General Public License v2.1</name>
            <url>https://github.com/alibaba/COLA/blob/master/LICENSE</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:https://github.com/alibaba/COLA.git</connection>
        <developerConnection>scm:git:https://github.com/alibaba/COLA.git</developerConnection>
        <url>https://github.com/alibaba/COLA</url>
    </scm>
    <issueManagement>
        <url>https://github.com/alibaba/COLA/issues</url>
        <system>GitHub Issues</system>
    </issueManagement>
    <developers>
        <developer>
            <id>significantfrank</id>
            <name>Frank Zhang</name>
            <email>25216348(at)qq.com</email>
            <roles>
                <role>Developer</role>
                <role>Architect</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/significantfrank</url>
        </developer>
        <developer>
            <id>oldratlee</id>
            <name>Jerry Lee</name>
            <email>oldratlee(at)gmail.com</email>
            <roles>
                <role>Developer</role>
                <role>CI/SCM Engineer</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/oldratlee</url>
        </developer>
    </developers>

    <modules>
        <module>cola-component-dto</module>
        <module>cola-component-exception</module>
        <module>cola-component-statemachine</module>
        <module>cola-component-domain-starter</module>
        <module>cola-component-extension-starter</module>
        <module>cola-component-catchlog-starter</module>
        <module>cola-component-test-container</module>
    </modules>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring.boot.version>2.2.6.RELEASE</spring.boot.version>
        <spring.version>5.2.2.RELEASE</spring.version>
        <lombok.version>1.18.16</lombok.version>
        <logback.version>1.1.11</logback.version>
    </properties>

    <dependencies>
        <!-- 测试包 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- 日志 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.9</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- 工具包 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.60</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>1.4</version>
            </dependency>

            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
                <scope>provided</scope>
            </dependency>

            <!-- 测试包 -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.8</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>gen-src+doc</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>3.2.1</version>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.2.0</version>
                        <executions>
                            <execution>
                                <id>attach-javadoc</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <source>8</source>
                            <show>protected</show>
                            <charset>UTF-8</charset>
                            <encoding>UTF-8</encoding>
                            <docencoding>UTF-8</docencoding>
                            <additionalJOptions>
                                <additionalJOption>--no-module-directories</additionalJOption>
                                <additionalJOption>-quiet</additionalJOption>
                                <additionalJOption>-J-Duser.language=en</additionalJOption>
                                <additionalJOption>-J-Duser.country=US</additionalJOption>
                                <additionalJOption>-Xdoclint:none</additionalJOption>
                            </additionalJOptions>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>gen-sign</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>gen-git-properties</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <!--
                        Maven plugin which includes build-time git repository information into an POJO / *.properties).
                        Make your apps tell you which version exactly they were built from! Priceless in large distributed deployments.
                            https://github.com/ktoso/maven-git-commit-id-plugin
                    -->
                    <plugin>
                        <groupId>pl.project13.maven</groupId>
                        <artifactId>git-commit-id-plugin</artifactId>
                        <version>4.0.3</version>
                        <executions>
                            <execution>
                                <id>get-the-git-infos</id>
                                <goals>
                                    <goal>revision</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>validate-the-git-infos</id>
                                <goals>
                                    <goal>validateRevision</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <validationProperties>
                                <!-- verify that the current repository is not dirty -->
                                <validationProperty>
                                    <name>validating git dirty</name>
                                    <value>${git.dirty}</value>
                                    <shouldMatchTo>false</shouldMatchTo>
                                </validationProperty>
                            </validationProperties>
                            <generateGitPropertiesFile>true</generateGitPropertiesFile>
                            <generateGitPropertiesFilename>${project.build.outputDirectory}/META-INF/scm/${project.groupId}/${project.artifactId}/git.properties</generateGitPropertiesFilename>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>force-jdk11-when-release</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <!--
                        add maven-enforce-plugin to make sure the right jdk is used
                        https://stackoverflow.com/a/18420462/922688
                    -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>3.0.0-M3</version>
                        <executions>
                            <execution>
                                <id>enforce-jdk-versions</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireJavaVersion>
                                            <version>11</version>
                                        </requireJavaVersion>
                                    </rules>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
