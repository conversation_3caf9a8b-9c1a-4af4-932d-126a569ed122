<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alibaba.cola</groupId>
        <artifactId>cola-components-parent</artifactId>
        <version>4.0.1</version>
    </parent>

    <artifactId>cola-component-dto</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}:${project.version}</name>
    <description>${project.artifactId}</description>
    <url>https://github.com/alibaba/COLA</url>

    <licenses>
        <license>
            <name>GNU Lesser General Public License v2.1</name>
            <url>https://github.com/alibaba/COLA/blob/master/LICENSE</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:https://github.com/alibaba/COLA.git</connection>
        <developerConnection>scm:git:https://github.com/alibaba/COLA.git</developerConnection>
        <url>https://github.com/alibaba/COLA</url>
    </scm>
    <issueManagement>
        <url>https://github.com/alibaba/COLA/issues</url>
        <system>GitHub Issues</system>
    </issueManagement>
    <developers>
        <developer>
            <id>significantfrank</id>
            <name>Frank Zhang</name>
            <email>25216348(at)qq.com</email>
            <roles>
                <role>Developer</role>
                <role>Architect</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/significantfrank</url>
        </developer>
        <developer>
            <id>oldratlee</id>
            <name>Jerry Lee</name>
            <email>oldratlee(at)gmail.com</email>
            <roles>
                <role>Developer</role>
                <role>CI/SCM Engineer</role>
            </roles>
            <timezone>+8</timezone>
            <url>https://github.com/oldratlee</url>
        </developer>
    </developers>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
