<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
<!--
	<parent>
		<groupId>com.alibaba</groupId>
		<artifactId>parent-pom</artifactId>
		<version>1.0.0</version>
	</parent>
-->
	<groupId>com.alibaba</groupId>
	<artifactId>fastjson</artifactId>
	<version>1.2.25</version>

	<packaging>jar</packaging>
	<name>fastjson</name>
	<description>Fastjson is a JSON processor (JSON parser + JSON generator) written in Java</description>

	<url>https://github.com/alibaba/fastjson</url>
	<inceptionYear>2012</inceptionYear>

	<properties>
		<junit.version>4.11</junit.version>
		<gpg.skip>false</gpg.skip>
		<javadoc.skip>false</javadoc.skip>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<jdk.version>1.5</jdk.version>
	</properties>

	<scm>
		<url>https://github.com/alibaba/fastjson</url>
		<connection>scm:git:https://**************/alibaba/fastjson.git</connection>
	</scm>

	<repositories>
		<repository>
			<id>local-file</id>
			<url>file://${basedir}/lib/</url>
			<layout>default</layout>
		</repository>
	</repositories>

	<organization>
		<name>Alibaba Group</name>
		<url>https://github.com/alibaba</url>
	</organization>
	
	<developers>
		<developer>
			<id>wenshao</id>
			<name>wenshao</name>
			<email><EMAIL></email>
		</developer>
		<developer>
			<id>axmanwang</id>
			<name>axmanwang</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<licenses>
		<license>
			<name>Apache 2</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
			<comments>A business-friendly OSS license</comments>
		</license>
	</licenses>

	<build>
		<extensions>
			<extension>
				<groupId>org.apache.maven.wagon</groupId>
				<artifactId>wagon-webdav</artifactId>
				<version>1.0-beta-2</version>
			</extension>
		</extensions>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>

				<version>3.5.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<source>${jdk.version}</source>
					<target>${jdk.version}</target>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.codehaus.plexus</groupId>
						<artifactId>plexus-compiler-javac</artifactId>
						<version>2.7</version>
					</dependency>
				</dependencies>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<attach>true</attach>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<includes>
						<include>**/bvt/**/*.java</include>
					</includes>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<skip>${javadoc.skip}</skip>
					<show>public</show>
					<charset>UTF-8</charset>
					<encoding>UTF-8</encoding>
					<docencoding>UTF-8</docencoding>
					<links>
						<link>http://docs.oracle.com/javase/6/docs/api</link>
					</links>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-gpg-plugin</artifactId>
				<configuration>
					<skip>${gpg.skip}</skip>
				</configuration>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<dependencies>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>javax.ws.rs</groupId>
			<artifactId>javax.ws.rs-api</artifactId>
			<version>2.0.1</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
			<version>3.1.2</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxrs</artifactId>
			<version>3.1.2</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-websocket</artifactId>
			<version>4.2.5.RELEASE</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<version>4.2.5.RELEASE</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.squareup.retrofit2</groupId>
			<artifactId>retrofit</artifactId>
			<version>2.1.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.5.0</version>
			<scope>provided</scope>
		</dependency>
				
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-spring-web</artifactId>
			<version>2.5.0</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-server</artifactId>
			<version>8.1.8.v20121106</version>
			<scope>test</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-webapp</artifactId>
			<version>8.1.8.v20121106</version>
			<scope>test</scope>
			<optional>true</optional>
		</dependency>


		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.7.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-module-afterburner</artifactId>
			<version>2.7.3</version>
			<scope>test</scope>
		</dependency>
		
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>2.2.2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.jaxrs</groupId>
			<artifactId>jackson-jaxrs-json-provider</artifactId>
			<version>2.7.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>1.4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.6.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<version>2.2.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.owlike</groupId>
			<artifactId>genson</artifactId>
			<version>1.4</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.owlike</groupId>
			<artifactId>genson-scala_2.11</artifactId>
			<version>1.4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.clojure</groupId>
			<artifactId>clojure</artifactId>
			<version>1.5.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy</artifactId>
			<version>2.1.5</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>4.2.5.RELEASE</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.18.0-GA</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-rs-client</artifactId>
			<version>3.1.2</version>
			<scope>test</scope>
		</dependency>
		
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-commons-core</artifactId>
			<version>1.4.1.RELEASE</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.glassfish.jersey.containers</groupId>
			<artifactId>jersey-container-servlet</artifactId>
			<version>2.21</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.core</groupId>
			<artifactId>jersey-client</artifactId>
			<version>2.21</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jersey.test-framework.providers</groupId>
			<artifactId>jersey-test-framework-provider-jdk-http</artifactId>
			<version>2.21</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.jsoniter</groupId>
			<artifactId>jsoniter</artifactId>
			<version>0.9.4</version>
			<scope>test</scope>
		</dependency>

    	</dependencies>
	<profiles>
		<profile>
			<id>travis</id>
			<activation>
				<property>
					<name>env.TRAVIS</name>
					<value>true</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<!-- for codecov.io -->
						<!-- config example: https://github.com/codecov/example-java -->
						<!-- plugin docs: http://eclemma.org/jacoco/trunk/doc/ -->
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.7.6.201602180812</version>
						<executions>
							<execution>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
							</execution>
							<execution>
								<id>report</id>
								<phase>test</phase>
								<goals>
									<goal>report</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

	</profiles>
</project>
