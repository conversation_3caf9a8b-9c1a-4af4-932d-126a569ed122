#NOTE: This is a Maven Resolver internal implementation file, its format can be changed without prior notice.
#Fri Jul 18 19:05:27 CST 2025
@default-nexus-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.lastUpdated=1752836707012
@default-thirdparty-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.lastUpdated=1752836727023
http\://maven.aliyun.com/nexus/content/groups/public/.lastUpdated=1752836727171
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.error=Could not transfer artifact com.datastax.oss\:java-driver-bom\:pom\:4.13.0 from/to nexus (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/)\: No route to host
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.error=Could not transfer artifact com.datastax.oss\:java-driver-bom\:pom\:4.13.0 from/to thirdparty (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty)\: No route to host
