#NOTE: This is a Maven Resolver internal implementation file, its format can be changed without prior notice.
#Fri Jul 18 19:39:21 CST 2025
@default-nexus-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.lastUpdated=1752838741265
@default-thirdparty-http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.lastUpdated=1752838761275
http\://maven.aliyun.com/nexus/content/groups/public/.lastUpdated=1752838761329
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/.error=Could not transfer artifact com.datastax.oss\:java-driver-bom\:pom\:4.6.1 from/to nexus (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/groups/public/)\: No route to host
http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty/.error=Could not transfer artifact com.datastax.oss\:java-driver-bom\:pom\:4.6.1 from/to thirdparty (http\://nexus.juhuiwangluokeji.com\:8081/nexus/content/repositories/thirdparty)\: No route to host
