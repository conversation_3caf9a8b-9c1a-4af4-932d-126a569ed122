<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.amazonaws</groupId>
    <artifactId>aws-java-sdk-pom</artifactId>
    <version>1.11.792</version>
  </parent>
  <groupId>com.amazonaws</groupId>
  <artifactId>aws-java-sdk-bom</artifactId>
  <packaging>pom</packaging>
  <name>AWS SDK for Java - BOM</name>
  <description>The AWS SDK for Java - BOM module provides dependency management for individual java clients.</description>
  <url>https://aws.amazon.com/sdkforjava</url>
  <dependencyManagement>
    <!-- The dependencies section in pom.xml is auto generated. No manual changes are allowed -->
    <dependencies>
    <dependency>
        <artifactId>aws-java-sdk-macie2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iotsitewise</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-synthetics</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codestarconnections</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-detective</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kinesisvideosignalingchannels</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ebs</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-augmentedairuntime</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-outposts</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codeguruprofiler</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codegurureviewer</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-frauddetector</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kendra</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-networkmanager</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-computeoptimizer</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-accessanalyzer</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-schemas</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-imagebuilder</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elasticinference</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iotsecuretunneling</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-appconfig</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-wafv2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-connectparticipant</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-migrationhubconfig</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-dataexchange</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sesv2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-marketplacecatalog</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ssooidc</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sso</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-savingsplans</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codestarnotifications</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-workmailmessageflow</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-qldbsession</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-qldb</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-forecastquery</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-forecast</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-lakeformation</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-eventbridge</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ec2instanceconnect</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-applicationinsights</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-servicequotas</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-personalizeevents</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-personalize</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-personalizeruntime</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ioteventsdata</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iotevents</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iotthingsgraph</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-groundstation</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediapackagevod</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-managedblockchain</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-textract</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-worklink</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-backup</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-docdb</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-apigatewayv2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-apigatewaymanagementapi</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kafka</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-appmesh</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-licensemanager</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-securityhub</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-fsx</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediaconnect</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kinesisanalyticsv2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-comprehendmedical</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-globalaccelerator</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-transfer</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-datasync</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-robomaker</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-amplify</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-quicksight</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-rdsdata</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-route53resolver</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ram</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-s3control</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-pinpointsmsvoice</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-pinpointemail</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-chime</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-signer</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-dlm</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-macie</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-eks</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediatailor</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-neptune</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-pi</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iot1clickprojects</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iot1clickdevices</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iotanalytics</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-acmpca</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-secretsmanager</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-fms</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-connect</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-transcribe</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-autoscalingplans</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-workmail</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-servicediscovery</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloud9</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-serverlessapplicationrepository</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-alexaforbusiness</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-resourcegroups</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-comprehend</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-translate</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sagemaker</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iotjobsdataplane</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sagemakerruntime</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kinesisvideo</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-appsync</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-guardduty</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mq</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediaconvert</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediastore</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediastoredata</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-medialive</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mediapackage</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-costexplorer</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-pricing</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mobile</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudhsmv2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-glue</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-migrationhub</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-dax</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-greengrass</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-athena</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-marketplaceentitlement</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codestar</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-lexmodelbuilding</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-resourcegroupstaggingapi</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-pinpoint</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-xray</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-opsworkscm</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-support</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-simpledb</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-servicecatalog</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-servermigration</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-simpleworkflow</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-storagegateway</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-route53</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-s3</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-importexport</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sts</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sqs</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-rds</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-redshift</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elasticbeanstalk</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-glacier</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iam</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-datapipeline</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elasticloadbalancing</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elasticloadbalancingv2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-emr</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elasticache</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elastictranscoder</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ec2</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-dynamodb</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-sns</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-budgets</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudtrail</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudwatch</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-logs</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-events</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cognitoidentity</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cognitosync</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-directconnect</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudformation</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudfront</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-clouddirectory</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kinesis</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-opsworks</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ses</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-autoscaling</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudsearch</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudwatchmetrics</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codedeploy</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codepipeline</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-kms</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-config</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-lambda</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ecs</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ecr</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cloudhsm</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-ssm</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-workspaces</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-machinelearning</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-directory</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-efs</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codecommit</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-devicefarm</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-elasticsearch</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-waf</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-marketplacecommerceanalytics</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-inspector</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-iot</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-api-gateway</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-acm</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-gamelift</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-dms</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-marketplacemeteringservice</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-cognitoidp</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-discovery</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-applicationautoscaling</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-snowball</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-rekognition</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-polly</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-lightsail</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-stepfunctions</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-health</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-costandusagereport</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-codebuild</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-appstream</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-shield</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-batch</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-lex</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-mechanicalturkrequester</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-organizations</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-workdocs</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-core</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-models</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>${awsjavasdk.version}</version>
    </dependency>
    <dependency>
        <artifactId>aws-java-sdk-swf-libraries</artifactId>
        <groupId>com.amazonaws</groupId>
        <optional>false</optional>
        <version>1.11.22</version>
    </dependency>
</dependencies>
  </dependencyManagement>
</project>
